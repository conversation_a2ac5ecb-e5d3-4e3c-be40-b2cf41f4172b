%load_ext autoreload
%autoreload 2

from evaluation.utils import analyze_sample_distributions
import json

# Load samples
samples_file = "/mnt/storage/xiaozhen/Water/Results/samples/swin_waternet_v20_2/divide/validation_indices.json"
# samples_file = "/mnt/storage/xiaozhen/Water/Results/samples/swin_waternet_v20_2/train_indices.json"
# samples_file = "/mnt/storage/xiaozhen/Water/Results/samples/swin_waternet_v20_2/val_indices.json"
with open(samples_file, 'r') as f:
    data = json.load(f)

if 'no_missing_samples' in data:
    samples = data['no_missing_samples']
else:
    samples = data

analyze_sample_distributions(samples, "./validation_samples")

len(samples)

len(samples)

analyze_sample_distributions(samples, "./validation_samples")

import pandas as pd
area_path = "/fossfs/xiaozhen/Water/Predictions/swin_waternet_v20_2/water_area.csv/monthly_water_area.csv"
level_path = "/fossfs/xiaozhen/Water/Predictions/swin_waternet_v20_2/water_area.csv/水位数据.xlsx"
filled_path = "/fossfs/xiaozhen/Water/Predictions/swin_waternet_v20_2/water_area.csv/water_filled_r16.csv"
area_df = pd.read_csv(area_path)
level_df = pd.read_excel(level_path)
filled_df = pd.read_csv(filled_path)


area_df['date'] = pd.to_datetime(area_df['month'], format='%Y-%m-%d')
area_df['year'] = area_df['date'].dt.year
area_df['month'] = area_df['date'].dt.month
area_df['area'] = area_df['area_km2']


level_df['date'] = pd.to_datetime(level_df['时间'], format='%Y-%m-%d')
level_df['year'] = level_df['date'].dt.year
level_df['month'] = level_df['date'].dt.month
level_df = pd.DataFrame(level_df.groupby(['year', 'month'])['水位'].mean())
level_df['level'] = level_df['水位']

filled_df['filled'] = filled_df['area']
filled_df = filled_df[['year', 'month', 'filled']]

temp = area_df.merge(level_df, on=['year', 'month'], how='inner')
df = temp.merge(filled_df, on=['year', 'month'], how='inner')

from scipy.stats import spearmanr
spearmanr(df['area'], df['level'])

from scipy.stats import spearmanr
spearmanr(df['filled'], df['level'])


import matplotlib.pyplot as plt
plt.plot(df['area'], df['level'], 'o')


import matplotlib.pyplot as plt
plt.plot(df['filled'], df['level'], 'o')

